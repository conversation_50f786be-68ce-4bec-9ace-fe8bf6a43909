<?php
/**
 * Environment Configuration File
 *
 * This file contains environment-specific settings for the Church Email Management System.
 * Modify these settings based on the deployment environment (development, staging, production).
 * All paths are now dynamic and organization-agnostic.
 */

// Detect the environment based on server name
$environment = 'development'; // Default to development

if (isset($_SERVER['SERVER_NAME'])) {
    if (strpos($_SERVER['SERVER_NAME'], 'profilepilot.io') !== false ||
        strpos($_SERVER['SERVER_NAME'], 'freedomassemblydb.online') !== false ||
        // Default to production if not on localhost
        (strpos($_SERVER['SERVER_NAME'], 'localhost') === false &&
         strpos($_SERVER['SERVER_NAME'], '127.0.0.1') === false)) {
        $environment = 'production';
    } elseif (strpos($_SERVER['SERVER_NAME'], 'staging') !== false) {
        $environment = 'staging';
    }
}

/**
 * Dynamic URL Detection Function
 * Automatically detects the correct base URL regardless of domain or subdirectory
 */
function detectBaseUrl() {
    $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off' || $_SERVER['SERVER_PORT'] == 443) ? "https://" : "http://";
    $host = $_SERVER['SERVER_NAME'] ?? 'localhost';

    // Get the current script path and determine the base directory
    $scriptPath = $_SERVER['SCRIPT_NAME'] ?? '';
    $requestUri = $_SERVER['REQUEST_URI'] ?? '';

    // Find the base path by looking for common patterns
    $basePath = '';

    // Check if we're in a subdirectory structure
    if (strpos($scriptPath, '/campaign/church/') !== false) {
        // Extract everything up to /campaign/church/
        $basePath = substr($scriptPath, 0, strpos($scriptPath, '/campaign/church/')) . '/campaign/church';
    } elseif (strpos($scriptPath, '/church/') !== false) {
        // Extract everything up to /church/
        $basePath = substr($scriptPath, 0, strpos($scriptPath, '/church/')) . '/church';
    } elseif (strpos($scriptPath, '/admin/') !== false) {
        // We're in admin directory, go up one level
        $basePath = dirname(dirname($scriptPath));
    } elseif (strpos($scriptPath, '/cron/') !== false) {
        // We're in cron directory, go up one level
        $basePath = dirname(dirname($scriptPath));
    } else {
        // Default fallback - try to detect from document root
        $documentRoot = $_SERVER['DOCUMENT_ROOT'] ?? '';
        $currentDir = dirname($_SERVER['SCRIPT_FILENAME'] ?? '');

        if ($documentRoot && $currentDir) {
            $relativePath = str_replace($documentRoot, '', $currentDir);
            $basePath = $relativePath;
        }
    }

    // Clean up the base path
    $basePath = rtrim($basePath, '/');

    return $protocol . $host . $basePath;
}

// Define base URLs for each environment
switch ($environment) {
    case 'production':
        // Use dynamic detection for production
        define('SITE_URL', detectBaseUrl());
        break;
    case 'staging':
        // Use dynamic detection for staging too
        define('SITE_URL', detectBaseUrl());
        break;
    case 'development':
    default:
        // For development, still allow override but default to dynamic
        if (!defined('SITE_URL')) {
            $detectedUrl = detectBaseUrl();
            // Fallback to localhost if detection fails
            define('SITE_URL', $detectedUrl ?: 'http://localhost/campaign/church');
        }
        break;
}

// Define asset and upload URLs (dynamic)
define('ASSETS_URL', SITE_URL . '/assets');
define('UPLOADS_URL', SITE_URL . '/uploads');
define('IMAGES_URL', ASSETS_URL . '/images');

// Dependency management setting
define('USE_LOCAL_ASSETS', $environment !== 'production'); // Use local assets in development and staging

// Organization-agnostic constants (will be made dynamic later)
define('CHURCH_NAME', 'Freedom Assembly Church International'); // TODO: Make dynamic from settings
define('CHURCH_LOGO', IMAGES_URL . '/banner.jpg');

// Dynamic email configuration based on current domain
$currentDomain = $_SERVER['SERVER_NAME'] ?? 'localhost';
if ($currentDomain === 'localhost' || strpos($currentDomain, '127.0.0.1') !== false) {
    // Development environment
    define('CHURCH_EMAIL', 'church@localhost');
    define('ADMIN_EMAIL', 'admin@localhost');
} else {
    // Production/staging - use current domain
    define('CHURCH_EMAIL', 'church@' . $currentDomain);
    define('ADMIN_EMAIL', 'admin@' . $currentDomain);
}

// Define admin URL - dynamic based on SITE_URL
define('ADMIN_URL', SITE_URL . '/admin');

// Define cron URL - dynamic based on SITE_URL
define('CRON_URL', SITE_URL . '/cron');

// Helper function to get current full URL
if (!function_exists('getCurrentUrl')) {
    function getCurrentUrl() {
        $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off' || $_SERVER['SERVER_PORT'] == 443) ? "https://" : "http://";
        $host = $_SERVER['SERVER_NAME'] ?? 'localhost';
        $uri = $_SERVER['REQUEST_URI'] ?? '';
        return $protocol . $host . $uri;
    }
}

// Helper function to build dynamic cron URLs
if (!function_exists('getCronUrl')) {
    function getCronUrl($script, $key = 'fac_2024_secure_cron_8x9q2p5m') {
        return CRON_URL . '/' . $script . '?cron_key=' . $key;
    }
}

// Return environment for reference
return $environment; 