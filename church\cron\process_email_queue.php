<?php
/**
 * Process Email Queue Cron Job
 * 
 * This script processes queued emails for the church management system.
 * It handles sending emails from the email queue with rate limiting and error handling.
 * 
 * Correct Cron Job Command (run every 5 minutes):
 * */5 * * * * wget -q -O /dev/null "https://demosender.online/campaign/church/cron/process_email_queue.php?cron_key=fac_2024_secure_cron_8x9q2p5m"
 * 
 * Previous INCORRECT syntax: * 5 1 1 1 (this would only run once per year!)
 * Fixed syntax: */5 * * * * (runs every 5 minutes)
 */

// Set script execution time limit to 5 minutes
set_time_limit(300);

// Define your secret key here - DO NOT SHARE THIS KEY
define('CRON_KEY', 'fac_2024_secure_cron_8x9q2p5m');

// Security check to allow for CLI execution and web requests
if (php_sapi_name() !== 'cli') {
    // Only check for cron_key when running via web request
    if (!isset($_GET['cron_key']) || $_GET['cron_key'] !== CRON_KEY) {
        header('HTTP/1.0 403 Forbidden');
        exit('Access Denied');
    }
}

// Include necessary files
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../includes/email_functions.php';

// Initialize logging
$logFile = __DIR__ . '/../logs/email_queue.log';
$logDir = dirname($logFile);

// Ensure log directory exists
if (!is_dir($logDir)) {
    mkdir($logDir, 0755, true);
}

/**
 * Email Queue Processor Class
 */
class EmailQueueProcessor {
    private $pdo;
    private $logFile;
    private $maxEmailsPerRun = 50; // Maximum emails to process per run
    private $delayBetweenEmails = 1; // Seconds to wait between emails
    
    public function __construct($pdo, $logFile) {
        $this->pdo = $pdo;
        $this->logFile = $logFile;
    }
    
    /**
     * Log a message with timestamp
     */
    private function log($message, $level = 'info') {
        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[$timestamp] [$level] $message" . PHP_EOL;
        file_put_contents($this->logFile, $logMessage, FILE_APPEND);
        
        // Also output to console if running via CLI
        if (php_sapi_name() === 'cli') {
            echo $logMessage;
        }
    }
    
    /**
     * Get pending emails from the queue
     */
    private function getPendingEmails($limit = null) {
        $limit = $limit ?: $this->maxEmailsPerRun;
        
        try {
            // Check if email_queue table exists, if not create it
            $this->ensureEmailQueueTable();
            
            $sql = "SELECT * FROM email_queue 
                    WHERE status = 'pending' 
                    AND (scheduled_at IS NULL OR scheduled_at <= NOW())
                    ORDER BY priority DESC, created_at ASC 
                    LIMIT ?";
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([$limit]);
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            $this->log("Error getting pending emails: " . $e->getMessage(), 'error');
            return [];
        }
    }
    
    /**
     * Ensure email_queue table exists
     */
    private function ensureEmailQueueTable() {
        $sql = "CREATE TABLE IF NOT EXISTS email_queue (
            id INT AUTO_INCREMENT PRIMARY KEY,
            recipient_email VARCHAR(255) NOT NULL,
            recipient_name VARCHAR(255),
            subject VARCHAR(500) NOT NULL,
            body TEXT NOT NULL,
            sender_email VARCHAR(255),
            sender_name VARCHAR(255),
            priority INT DEFAULT 5,
            status ENUM('pending', 'sent', 'failed', 'cancelled') DEFAULT 'pending',
            attempts INT DEFAULT 0,
            max_attempts INT DEFAULT 3,
            scheduled_at DATETIME NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            sent_at DATETIME NULL,
            error_message TEXT NULL,
            email_type VARCHAR(50) DEFAULT 'general',
            template_id INT NULL,
            member_id INT NULL,
            INDEX idx_status (status),
            INDEX idx_scheduled (scheduled_at),
            INDEX idx_priority (priority)
        )";
        
        $this->pdo->exec($sql);
    }
    
    /**
     * Process a single email from the queue
     */
    private function processEmail($emailData) {
        try {
            // Update attempts count
            $this->updateEmailAttempts($emailData['id']);
            
            // Send the email
            $result = $this->sendQueuedEmail($emailData);
            
            if ($result['success']) {
                // Mark as sent
                $this->updateEmailStatus($emailData['id'], 'sent', null);
                $this->log("Email sent successfully to {$emailData['recipient_email']}", 'success');
                return true;
            } else {
                // Check if we should retry or mark as failed
                $newAttempts = $emailData['attempts'] + 1;
                if ($newAttempts >= $emailData['max_attempts']) {
                    $this->updateEmailStatus($emailData['id'], 'failed', $result['error']);
                    $this->log("Email failed permanently to {$emailData['recipient_email']}: {$result['error']}", 'error');
                } else {
                    $this->updateEmailStatus($emailData['id'], 'pending', $result['error']);
                    $this->log("Email failed temporarily to {$emailData['recipient_email']}: {$result['error']} (attempt $newAttempts)", 'warning');
                }
                return false;
            }
            
        } catch (Exception $e) {
            $this->log("Error processing email ID {$emailData['id']}: " . $e->getMessage(), 'error');
            $this->updateEmailStatus($emailData['id'], 'failed', $e->getMessage());
            return false;
        }
    }
    
    /**
     * Send a queued email
     */
    private function sendQueuedEmail($emailData) {
        try {
            // Use the existing sendEmail function if available
            if (function_exists('sendEmail')) {
                $success = sendEmail(
                    $emailData['recipient_email'],
                    $emailData['subject'],
                    $emailData['body'],
                    $emailData['recipient_name'] ?: 'Member',
                    $emailData['sender_email'],
                    $emailData['sender_name']
                );
                
                return [
                    'success' => $success,
                    'error' => $success ? null : 'Email sending failed'
                ];
            } else {
                return [
                    'success' => false,
                    'error' => 'sendEmail function not available'
                ];
            }
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Update email status in the queue
     */
    private function updateEmailStatus($emailId, $status, $errorMessage = null) {
        $sql = "UPDATE email_queue SET 
                status = ?, 
                error_message = ?,
                sent_at = CASE WHEN ? = 'sent' THEN NOW() ELSE sent_at END
                WHERE id = ?";
        
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$status, $errorMessage, $status, $emailId]);
    }
    
    /**
     * Update email attempts count
     */
    private function updateEmailAttempts($emailId) {
        $sql = "UPDATE email_queue SET attempts = attempts + 1 WHERE id = ?";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$emailId]);
    }
    
    /**
     * Process all pending emails in the queue
     */
    public function processQueue() {
        $this->log("Starting email queue processing");
        
        $pendingEmails = $this->getPendingEmails();
        $totalEmails = count($pendingEmails);
        
        if ($totalEmails === 0) {
            $this->log("No pending emails in queue");
            return;
        }
        
        $this->log("Found $totalEmails pending emails to process");
        
        $successCount = 0;
        $failureCount = 0;
        
        foreach ($pendingEmails as $email) {
            if ($this->processEmail($email)) {
                $successCount++;
            } else {
                $failureCount++;
            }
            
            // Add delay between emails to prevent overwhelming the SMTP server
            if ($this->delayBetweenEmails > 0) {
                sleep($this->delayBetweenEmails);
            }
        }
        
        $this->log("Email queue processing completed. Sent: $successCount, Failed: $failureCount");
    }
    
    /**
     * Clean up old processed emails (older than 30 days)
     */
    public function cleanupOldEmails() {
        try {
            $sql = "DELETE FROM email_queue 
                    WHERE status IN ('sent', 'failed') 
                    AND created_at < DATE_SUB(NOW(), INTERVAL 30 DAY)";
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute();
            
            $deletedCount = $stmt->rowCount();
            if ($deletedCount > 0) {
                $this->log("Cleaned up $deletedCount old emails from queue");
            }
            
        } catch (Exception $e) {
            $this->log("Error cleaning up old emails: " . $e->getMessage(), 'error');
        }
    }
}

// Main execution
try {
    $processor = new EmailQueueProcessor($pdo, $logFile);
    
    // Process the email queue
    $processor->processQueue();
    
    // Clean up old emails (run cleanup once per day)
    if (date('H:i') === '02:00') {
        $processor->cleanupOldEmails();
    }
    
    $log_message = '[' . date('Y-m-d H:i:s') . '] Email queue processing completed successfully.' . PHP_EOL;
    file_put_contents($logFile, $log_message, FILE_APPEND);
    
} catch (Exception $e) {
    $error_message = '[' . date('Y-m-d H:i:s') . '] Email queue processing failed: ' . $e->getMessage() . PHP_EOL;
    file_put_contents($logFile, $error_message, FILE_APPEND);
    
    // Output error for CLI
    if (php_sapi_name() === 'cli') {
        echo $error_message;
    }
}

// Output success message for web requests
if (php_sapi_name() !== 'cli') {
    echo json_encode([
        'status' => 'success',
        'message' => 'Email queue processing completed',
        'timestamp' => date('Y-m-d H:i:s')
    ]);
}
?>
