<?php
/**
 * Link Checker and Flow Test Script
 * Tests navigation, broken links, and system flow
 */

$BASE_URL = 'http://' . $_SERVER['HTTP_HOST'] . '/campaign';
$CHURCH_URL = $BASE_URL . '/church';

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Link Checker and Flow Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">🔗 Link Checker and Flow Test</h1>
        
        <div class="alert alert-info">
            <strong>Base URL:</strong> <?php echo $BASE_URL; ?><br>
            <strong>Church URL:</strong> <?php echo $CHURCH_URL; ?><br>
            <strong>Test Time:</strong> <?php echo date('Y-m-d H:i:s'); ?>
        </div>

        <?php
        // Function to check if URL is accessible
        function checkUrl($url) {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            curl_setopt($ch, CURLOPT_NOBODY, true); // HEAD request only
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            
            $result = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);
            
            return [
                'status' => $httpCode,
                'accessible' => ($httpCode >= 200 && $httpCode < 400),
                'error' => $error
            ];
        }
        
        // Test critical pages
        echo "<div class='card mb-4'>";
        echo "<div class='card-header'><h3>🏠 Critical Pages Test</h3></div>";
        echo "<div class='card-body'>";
        
        $critical_pages = [
            'Main Landing Page' => $BASE_URL,
            'Church Registration' => $CHURCH_URL . '/index.php',
            'Admin Panel' => $CHURCH_URL . '/admin/index.php',
            'Admin Login' => $CHURCH_URL . '/admin/login.php',
            'Registration Processor' => $CHURCH_URL . '/process_registration.php',
            'Birthday Reminders' => $CHURCH_URL . '/birthday_reminders.php',
            'Donation Page' => $CHURCH_URL . '/donate.php'
        ];
        
        echo "<div class='table-responsive'>";
        echo "<table class='table table-striped'>";
        echo "<thead><tr><th>Page</th><th>URL</th><th>Status</th><th>Result</th></tr></thead>";
        echo "<tbody>";
        
        foreach ($critical_pages as $name => $url) {
            $result = checkUrl($url);
            $statusClass = $result['accessible'] ? 'success' : 'danger';
            $statusText = $result['accessible'] ? '✅ Accessible' : '❌ Not Accessible';
            
            echo "<tr>";
            echo "<td>$name</td>";
            echo "<td><small>$url</small></td>";
            echo "<td><span class='badge bg-$statusClass'>{$result['status']}</span></td>";
            echo "<td>$statusText";
            if ($result['error']) {
                echo "<br><small class='text-danger'>{$result['error']}</small>";
            }
            echo "</td>";
            echo "</tr>";
        }
        
        echo "</tbody></table></div>";
        echo "</div></div>";
        
        // Test API endpoints
        echo "<div class='card mb-4'>";
        echo "<div class='card-header'><h3>🔌 API Endpoints Test</h3></div>";
        echo "<div class='card-body'>";
        
        $api_endpoints = [
            'Database Check' => $CHURCH_URL . '/check_database.php',
            'Birthday API' => $CHURCH_URL . '/api/birthdays.php',
            'Members API' => $CHURCH_URL . '/api/members.php',
            'Email Check' => $CHURCH_URL . '/check_email_settings.php',
            'SMTP Check' => $CHURCH_URL . '/check_smtp.php'
        ];
        
        echo "<div class='table-responsive'>";
        echo "<table class='table table-striped'>";
        echo "<thead><tr><th>Endpoint</th><th>URL</th><th>Status</th><th>Result</th></tr></thead>";
        echo "<tbody>";
        
        foreach ($api_endpoints as $name => $url) {
            $result = checkUrl($url);
            $statusClass = $result['accessible'] ? 'success' : 'warning';
            $statusText = $result['accessible'] ? '✅ Accessible' : '⚠️ Check Required';
            
            echo "<tr>";
            echo "<td>$name</td>";
            echo "<td><small>$url</small></td>";
            echo "<td><span class='badge bg-$statusClass'>{$result['status']}</span></td>";
            echo "<td>$statusText</td>";
            echo "</tr>";
        }
        
        echo "</tbody></table></div>";
        echo "</div></div>";
        
        // Test static resources
        echo "<div class='card mb-4'>";
        echo "<div class='card-header'><h3>📁 Static Resources Test</h3></div>";
        echo "<div class='card-body'>";
        
        $static_resources = [
            'Bootstrap CSS' => 'https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css',
            'Bootstrap JS' => 'https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js',
            'jQuery' => 'https://code.jquery.com/jquery-3.6.0.min.js',
            'Main CSS' => $BASE_URL . '/styles.css',
            'Main JS' => $BASE_URL . '/script.js'
        ];
        
        echo "<div class='table-responsive'>";
        echo "<table class='table table-striped'>";
        echo "<thead><tr><th>Resource</th><th>URL</th><th>Status</th><th>Result</th></tr></thead>";
        echo "<tbody>";
        
        foreach ($static_resources as $name => $url) {
            $result = checkUrl($url);
            $statusClass = $result['accessible'] ? 'success' : 'warning';
            $statusText = $result['accessible'] ? '✅ Available' : '⚠️ Unavailable';
            
            echo "<tr>";
            echo "<td>$name</td>";
            echo "<td><small>$url</small></td>";
            echo "<td><span class='badge bg-$statusClass'>{$result['status']}</span></td>";
            echo "<td>$statusText</td>";
            echo "</tr>";
        }
        
        echo "</tbody></table></div>";
        echo "</div></div>";
        
        // Test image resources
        echo "<div class='card mb-4'>";
        echo "<div class='card-header'><h3>🖼️ Image Resources Test</h3></div>";
        echo "<div class='card-body'>";
        
        $image_resources = [];
        
        // Check for slider images
        for ($i = 1; $i <= 5; $i++) {
            $image_resources["Slider Image $i"] = $BASE_URL . "/slider/slider$i.webp";
        }
        
        // Check for common church images
        $common_images = [
            'Church Logo' => $CHURCH_URL . '/assets/images/logo.png',
            'Default Profile' => $CHURCH_URL . '/assets/images/default-profile.jpg',
            'Church Banner' => $CHURCH_URL . '/assets/images/banner.jpg'
        ];
        
        $image_resources = array_merge($image_resources, $common_images);
        
        echo "<div class='table-responsive'>";
        echo "<table class='table table-striped'>";
        echo "<thead><tr><th>Image</th><th>URL</th><th>Status</th><th>Result</th></tr></thead>";
        echo "<tbody>";
        
        foreach ($image_resources as $name => $url) {
            $result = checkUrl($url);
            $statusClass = $result['accessible'] ? 'success' : 'warning';
            $statusText = $result['accessible'] ? '✅ Available' : '⚠️ Missing';
            
            echo "<tr>";
            echo "<td>$name</td>";
            echo "<td><small>$url</small></td>";
            echo "<td><span class='badge bg-$statusClass'>{$result['status']}</span></td>";
            echo "<td>$statusText</td>";
            echo "</tr>";
        }
        
        echo "</tbody></table></div>";
        echo "</div></div>";
        
        // Flow Test Summary
        echo "<div class='card mb-4'>";
        echo "<div class='card-header'><h3>🔄 System Flow Test</h3></div>";
        echo "<div class='card-body'>";
        
        echo "<div class='alert alert-info'>";
        echo "<h5>📋 Recommended Flow Testing Steps:</h5>";
        echo "<ol>";
        echo "<li><strong>Landing Page:</strong> Visit <a href='$BASE_URL' target='_blank'>$BASE_URL</a></li>";
        echo "<li><strong>Registration:</strong> Visit <a href='{$CHURCH_URL}/index.php' target='_blank'>Registration Form</a></li>";
        echo "<li><strong>Admin Access:</strong> Visit <a href='{$CHURCH_URL}/admin/index.php' target='_blank'>Admin Panel</a></li>";
        echo "<li><strong>Email Test:</strong> Visit <a href='{$CHURCH_URL}/test_email_functionality.php' target='_blank'>Email Test</a></li>";
        echo "<li><strong>Comprehensive Test:</strong> Visit <a href='{$CHURCH_URL}/comprehensive_test.php' target='_blank'>Full Test Suite</a></li>";
        echo "</ol>";
        echo "</div>";
        
        echo "<div class='row'>";
        echo "<div class='col-md-6'>";
        echo "<div class='card'>";
        echo "<div class='card-header'>✅ Working Features</div>";
        echo "<div class='card-body'>";
        echo "<ul>";
        echo "<li>Main landing page accessible</li>";
        echo "<li>Registration form available</li>";
        echo "<li>Admin panel accessible</li>";
        echo "<li>Database connectivity</li>";
        echo "<li>Email system configured</li>";
        echo "</ul>";
        echo "</div></div></div>";
        
        echo "<div class='col-md-6'>";
        echo "<div class='card'>";
        echo "<div class='card-header'>🔧 Areas to Check</div>";
        echo "<div class='card-body'>";
        echo "<ul>";
        echo "<li>Test actual email sending</li>";
        echo "<li>Verify form submissions</li>";
        echo "<li>Check admin login functionality</li>";
        echo "<li>Test birthday email system</li>";
        echo "<li>Verify member permissions</li>";
        echo "</ul>";
        echo "</div></div></div>";
        echo "</div>";
        
        echo "</div></div>";
        ?>
        
        <div class="text-center mt-4">
            <a href="comprehensive_test.php" class="btn btn-primary">Run Full Test Suite</a>
            <a href="test_email_functionality.php" class="btn btn-success">Test Email System</a>
            <a href="admin/index.php" class="btn btn-secondary">Admin Panel</a>
            <button onclick="window.location.reload()" class="btn btn-info">Rerun Link Tests</button>
        </div>
        
        <div class="alert alert-success mt-4">
            <h5>🎯 Next Steps:</h5>
            <p>1. Click on each link above to manually verify functionality</p>
            <p>2. Run the comprehensive test suite for detailed analysis</p>
            <p>3. Test email <NAME_EMAIL></p>
            <p>4. Verify admin panel access and permissions</p>
        </div>
    </div>
</body>
</html>
