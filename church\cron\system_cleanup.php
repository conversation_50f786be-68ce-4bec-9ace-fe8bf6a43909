<?php
/**
 * System Cleanup Cron Job
 * 
 * This script performs weekly system maintenance tasks including:
 * - Cleaning up old log files
 * - Removing expired email tracking data
 * - Cleaning up old email queue entries
 * - Optimizing database tables
 * 
 * Recommended Cron Job Command (run weekly on Sundays at 2 AM):
 * 0 2 * * 0 wget -q -O /dev/null "https://demosender.online/campaign/church/cron/system_cleanup.php?cron_key=fac_2024_secure_cron_8x9q2p5m"
 */

// Set script execution time limit to 10 minutes
set_time_limit(600);

// Define your secret key here - DO NOT SHARE THIS KEY
define('CRON_KEY', 'fac_2024_secure_cron_8x9q2p5m');

// Security check to allow for CLI execution and web requests
if (php_sapi_name() !== 'cli') {
    // Only check for cron_key when running via web request
    if (!isset($_GET['cron_key']) || $_GET['cron_key'] !== CRON_KEY) {
        header('HTTP/1.0 403 Forbidden');
        exit('Access Denied');
    }
}

// Include necessary files
require_once __DIR__ . '/../config.php';

// Initialize logging
$logFile = __DIR__ . '/../logs/system_cleanup.log';
$logDir = dirname($logFile);

// Ensure log directory exists
if (!is_dir($logDir)) {
    mkdir($logDir, 0755, true);
}

/**
 * System Cleanup Class
 */
class SystemCleanup {
    private $pdo;
    private $logFile;
    private $logDir;
    
    public function __construct($pdo, $logFile) {
        $this->pdo = $pdo;
        $this->logFile = $logFile;
        $this->logDir = dirname($logFile);
    }
    
    /**
     * Log a message with timestamp
     */
    private function log($message, $level = 'info') {
        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[$timestamp] [$level] $message" . PHP_EOL;
        file_put_contents($this->logFile, $logMessage, FILE_APPEND);
        
        // Also output to console if running via CLI
        if (php_sapi_name() === 'cli') {
            echo $logMessage;
        }
    }
    
    /**
     * Clean up old log files (older than 90 days)
     */
    public function cleanupLogFiles() {
        $this->log("Starting log file cleanup");
        
        try {
            $logFiles = glob($this->logDir . '/*.log');
            $cleanedCount = 0;
            $cutoffDate = time() - (90 * 24 * 60 * 60); // 90 days ago
            
            foreach ($logFiles as $logFile) {
                // Skip the current cleanup log
                if (basename($logFile) === 'system_cleanup.log') {
                    continue;
                }
                
                if (filemtime($logFile) < $cutoffDate) {
                    if (unlink($logFile)) {
                        $cleanedCount++;
                        $this->log("Deleted old log file: " . basename($logFile));
                    }
                }
            }
            
            $this->log("Log file cleanup completed. Deleted $cleanedCount old log files");
            
        } catch (Exception $e) {
            $this->log("Error during log file cleanup: " . $e->getMessage(), 'error');
        }
    }
    
    /**
     * Clean up old email tracking data (older than 6 months)
     */
    public function cleanupEmailTracking() {
        $this->log("Starting email tracking cleanup");
        
        try {
            // Clean up old email tracking records
            $sql = "DELETE FROM email_tracking WHERE created_at < DATE_SUB(NOW(), INTERVAL 6 MONTH)";
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute();
            $deletedTracking = $stmt->rowCount();
            
            // Clean up old email logs
            $sql = "DELETE FROM email_logs WHERE sent_at < DATE_SUB(NOW(), INTERVAL 6 MONTH)";
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute();
            $deletedLogs = $stmt->rowCount();
            
            $this->log("Email tracking cleanup completed. Deleted $deletedTracking tracking records and $deletedLogs log entries");
            
        } catch (Exception $e) {
            $this->log("Error during email tracking cleanup: " . $e->getMessage(), 'error');
        }
    }
    
    /**
     * Clean up old email queue entries (older than 30 days)
     */
    public function cleanupEmailQueue() {
        $this->log("Starting email queue cleanup");
        
        try {
            $sql = "DELETE FROM email_queue 
                    WHERE status IN ('sent', 'failed', 'cancelled') 
                    AND created_at < DATE_SUB(NOW(), INTERVAL 30 DAY)";
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute();
            $deletedCount = $stmt->rowCount();
            
            $this->log("Email queue cleanup completed. Deleted $deletedCount old queue entries");
            
        } catch (Exception $e) {
            $this->log("Error during email queue cleanup: " . $e->getMessage(), 'error');
        }
    }
    
    /**
     * Clean up old admin activity logs (older than 1 year)
     */
    public function cleanupActivityLogs() {
        $this->log("Starting activity logs cleanup");
        
        try {
            $sql = "DELETE FROM admin_activity_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL 1 YEAR)";
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute();
            $deletedCount = $stmt->rowCount();
            
            $this->log("Activity logs cleanup completed. Deleted $deletedCount old activity records");
            
        } catch (Exception $e) {
            $this->log("Error during activity logs cleanup: " . $e->getMessage(), 'error');
        }
    }
    
    /**
     * Clean up old login attempts (older than 3 months)
     */
    public function cleanupLoginAttempts() {
        $this->log("Starting login attempts cleanup");
        
        try {
            $sql = "DELETE FROM admin_login_attempts WHERE attempt_time < DATE_SUB(NOW(), INTERVAL 3 MONTH)";
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute();
            $deletedCount = $stmt->rowCount();
            
            $this->log("Login attempts cleanup completed. Deleted $deletedCount old login attempt records");
            
        } catch (Exception $e) {
            $this->log("Error during login attempts cleanup: " . $e->getMessage(), 'error');
        }
    }
    
    /**
     * Optimize database tables
     */
    public function optimizeTables() {
        $this->log("Starting database optimization");
        
        try {
            // Get all tables in the database
            $sql = "SHOW TABLES";
            $stmt = $this->pdo->query($sql);
            $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            $optimizedCount = 0;
            foreach ($tables as $table) {
                try {
                    $sql = "OPTIMIZE TABLE `$table`";
                    $this->pdo->exec($sql);
                    $optimizedCount++;
                } catch (Exception $e) {
                    $this->log("Failed to optimize table $table: " . $e->getMessage(), 'warning');
                }
            }
            
            $this->log("Database optimization completed. Optimized $optimizedCount tables");
            
        } catch (Exception $e) {
            $this->log("Error during database optimization: " . $e->getMessage(), 'error');
        }
    }
    
    /**
     * Clean up temporary files and cache
     */
    public function cleanupTempFiles() {
        $this->log("Starting temporary files cleanup");
        
        try {
            $cleanedCount = 0;
            
            // Clean up cache directory
            $cacheDir = __DIR__ . '/../cache';
            if (is_dir($cacheDir)) {
                $cacheFiles = glob($cacheDir . '/*.cache');
                $cutoffDate = time() - (24 * 60 * 60); // 1 day ago
                
                foreach ($cacheFiles as $cacheFile) {
                    if (filemtime($cacheFile) < $cutoffDate) {
                        if (unlink($cacheFile)) {
                            $cleanedCount++;
                        }
                    }
                }
            }
            
            // Clean up any temporary upload files older than 1 day
            $uploadsDir = __DIR__ . '/../uploads';
            if (is_dir($uploadsDir)) {
                $tempFiles = glob($uploadsDir . '/temp_*');
                $cutoffDate = time() - (24 * 60 * 60); // 1 day ago
                
                foreach ($tempFiles as $tempFile) {
                    if (filemtime($tempFile) < $cutoffDate) {
                        if (unlink($tempFile)) {
                            $cleanedCount++;
                        }
                    }
                }
            }
            
            $this->log("Temporary files cleanup completed. Deleted $cleanedCount temporary files");
            
        } catch (Exception $e) {
            $this->log("Error during temporary files cleanup: " . $e->getMessage(), 'error');
        }
    }
    
    /**
     * Generate cleanup summary report
     */
    public function generateSummaryReport() {
        $this->log("=== WEEKLY SYSTEM CLEANUP SUMMARY ===");
        $this->log("Cleanup completed on: " . date('Y-m-d H:i:s'));
        
        try {
            // Get database size
            $sql = "SELECT 
                        ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS 'DB Size (MB)'
                    FROM information_schema.tables 
                    WHERE table_schema = DATABASE()";
            $stmt = $this->pdo->query($sql);
            $dbSize = $stmt->fetchColumn();
            
            $this->log("Current database size: {$dbSize} MB");
            
            // Get member count
            $sql = "SELECT COUNT(*) FROM members";
            $stmt = $this->pdo->query($sql);
            $memberCount = $stmt->fetchColumn();
            
            $this->log("Total members: $memberCount");
            
            // Get email statistics
            $sql = "SELECT 
                        COUNT(*) as total_emails,
                        SUM(CASE WHEN status = 'sent' THEN 1 ELSE 0 END) as sent_emails,
                        SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed_emails
                    FROM email_logs 
                    WHERE sent_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)";
            $stmt = $this->pdo->query($sql);
            $emailStats = $stmt->fetch(PDO::FETCH_ASSOC);
            
            $this->log("Emails this week - Total: {$emailStats['total_emails']}, Sent: {$emailStats['sent_emails']}, Failed: {$emailStats['failed_emails']}");
            
        } catch (Exception $e) {
            $this->log("Error generating summary report: " . $e->getMessage(), 'error');
        }
        
        $this->log("=== END CLEANUP SUMMARY ===");
    }
    
    /**
     * Run all cleanup tasks
     */
    public function runAllCleanupTasks() {
        $this->log("Starting weekly system cleanup");
        
        $this->cleanupLogFiles();
        $this->cleanupEmailTracking();
        $this->cleanupEmailQueue();
        $this->cleanupActivityLogs();
        $this->cleanupLoginAttempts();
        $this->cleanupTempFiles();
        $this->optimizeTables();
        $this->generateSummaryReport();
        
        $this->log("Weekly system cleanup completed successfully");
    }
}

// Main execution
try {
    $cleanup = new SystemCleanup($pdo, $logFile);
    $cleanup->runAllCleanupTasks();
    
} catch (Exception $e) {
    $error_message = '[' . date('Y-m-d H:i:s') . '] System cleanup failed: ' . $e->getMessage() . PHP_EOL;
    file_put_contents($logFile, $error_message, FILE_APPEND);
    
    // Output error for CLI
    if (php_sapi_name() === 'cli') {
        echo $error_message;
    }
}

// Output success message for web requests
if (php_sapi_name() !== 'cli') {
    echo json_encode([
        'status' => 'success',
        'message' => 'System cleanup completed',
        'timestamp' => date('Y-m-d H:i:s')
    ]);
}
?>
