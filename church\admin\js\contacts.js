/**
 * Church Admin - Contact Management JavaScript
 * 
 * This file contains functions for handling contact data in the admin panel,
 * including pagination, selection, and group management.
 */

// Global variables for state management
let selectedContactIds = [];
let allAvailableContacts = [];
let currentPage = 1;
let itemsPerPage = 50;
let currentSort = 'name';
let currentOrder = 'ASC';
let currentGroupId = '';

// Process contact groups to ensure uniqueness
window.renderGroups = function(groups) {
    if (!groups || groups.length === 0) {
        return '<span class="badge bg-secondary">No Groups</span>';
    }
    
    const uniqueGroups = [...new Set(groups)];
    const maxDisplay = 3;
    
    let html = '<div class="group-badges-container">';
    const displayGroups = uniqueGroups.slice(0, maxDisplay);
    
    displayGroups.forEach(group => {
        html += `<span class="badge bg-info me-1">${escapeHtml(group)}</span>`;
    });
    
    if (uniqueGroups.length > maxDisplay) {
        html += `<span class="badge bg-secondary">+${uniqueGroups.length - maxDisplay} more</span>`;
    }
    
    html += '</div>';
    return html;
}

// Helper function to escape HTML
window.escapeHtml = function(text) {
    if (!text) return '';
    const map = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#039;'
    };
    return text.replace(/[&<>"']/g, m => map[m]);
}

// Function to load contacts via AJAX
function loadContacts(page = currentPage) {
    console.log('Loading contacts:', { page, itemsPerPage, currentSort, currentOrder, currentGroupId });
    
    const url = `ajax/get_contacts.php?page=${page}&sort=${currentSort}&order=${currentOrder}&limit=${itemsPerPage}&get_all=true${currentGroupId ? '&group_id=' + currentGroupId : ''}`;
    
    // Show loading state
    const tbody = document.querySelector('#contactsTable tbody');
    if (tbody) {
        tbody.innerHTML = '<tr><td colspan="6" class="text-center"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></td></tr>';
    }
    
    fetch(url)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                // Store all contacts for bulk operations
                allAvailableContacts = data.all_contacts || [];
                
                // Update table content
                updateContactsTable(data.contacts);
                
                // Update pagination
                updatePagination(data.pagination);
                
                // Update selection info
                updateSelectionInfo();
                
                // Reinitialize event handlers
                initializeContactActions();
            } else {
                throw new Error(data.error || 'Failed to load contacts');
            }
        })
        .catch(error => {
            console.error('Error loading contacts:', error);
            showNotification('Error loading contacts: ' + error.message, 'error');
            
            if (tbody) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="6" class="text-center text-danger">
                            <i class="bi bi-exclamation-triangle-fill me-2"></i>
                            ${error.message}
                        </td>
                    </tr>
                `;
            }
        });
}

// Update contacts table with data
function updateContactsTable(contacts) {
    const tbody = document.querySelector('#contactsTable tbody');
    if (!tbody) return;
    
    tbody.innerHTML = '';
    
    contacts.forEach((contact, index) => {
        const isSelected = selectedContactIds.includes(contact.id.toString());
        
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td>
                <input type="checkbox" class="form-check-input contact-checkbox" 
                       name="selected_contacts[]" value="${contact.id}" 
                       ${isSelected ? 'checked' : ''}>
            </td>
            <td>${escapeHtml(contact.name)}</td>
            <td>${escapeHtml(contact.email)}</td>
            <td>${renderGroups(contact.groups)}</td>
            <td>${new Date(contact.created_at).toLocaleDateString()}</td>
        `;
        tbody.appendChild(tr);
    });
    
    // Update "Select All" checkbox state
    updateSelectAllCheckbox();
}

// Update pagination controls
function updatePagination(pagination) {
    const paginationContainer = document.querySelector('#contactPagination');
    if (!paginationContainer) return;
    
    const totalPages = Math.ceil(pagination.total / itemsPerPage);
    let html = '';
    
    // Previous button
    html += `
        <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" data-page="${currentPage - 1}">&laquo;</a>
        </li>
    `;
    
    // Page numbers
    for (let i = 1; i <= totalPages; i++) {
        if (
            i === 1 || 
            i === totalPages || 
            (i >= currentPage - 2 && i <= currentPage + 2)
        ) {
            html += `
                <li class="page-item ${i === currentPage ? 'active' : ''}">
                    <a class="page-link" href="#" data-page="${i}">${i}</a>
                </li>
            `;
        } else if (
            i === currentPage - 3 || 
            i === currentPage + 3
        ) {
            html += `
                <li class="page-item disabled">
                    <span class="page-link">...</span>
                </li>
            `;
        }
    }
    
    // Next button
    html += `
        <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
            <a class="page-link" href="#" data-page="${currentPage + 1}">&raquo;</a>
        </li>
    `;
    
    paginationContainer.innerHTML = html;
    
    // Add click handlers to pagination links
    paginationContainer.querySelectorAll('.page-link').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const page = parseInt(this.dataset.page);
            if (page && !isNaN(page)) {
                currentPage = page;
                loadContacts(page);
            }
        });
    });
}

// Update selection info display
function updateSelectionInfo() {
    const infoElement = document.getElementById('contactSelectionInfo');
    if (!infoElement) return;
    
    infoElement.innerHTML = `
        <span class="badge bg-primary">${selectedContactIds.length} contacts selected</span>
        <small class="text-muted ms-2">out of ${allAvailableContacts.length} total</small>
    `;
}

// Update "Select All" checkbox state
function updateSelectAllCheckbox() {
    const selectAllCheckbox = document.getElementById('selectAllContactsCheckbox');
    if (!selectAllCheckbox) return;
    
    const checkboxes = document.querySelectorAll('.contact-checkbox');
    const checkedCount = document.querySelectorAll('.contact-checkbox:checked').length;
    
    selectAllCheckbox.checked = checkboxes.length > 0 && checkedCount === checkboxes.length;
    selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < checkboxes.length;
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Initialize contact table functionality
    const contactsTable = document.getElementById('contactsTable');
    if (!contactsTable) return;
    
    // Initialize pagination
    loadContacts();
    
    // Handle "Select All" checkbox
    const selectAllCheckbox = document.getElementById('selectAllContactsCheckbox');
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('.contact-checkbox');
            checkboxes.forEach(cb => {
                cb.checked = this.checked;
                const contactId = cb.value;
                
                if (this.checked) {
                    if (!selectedContactIds.includes(contactId)) {
                        selectedContactIds.push(contactId);
                    }
                } else {
                    const index = selectedContactIds.indexOf(contactId);
                    if (index > -1) {
                        selectedContactIds.splice(index, 1);
                    }
                }
            });
            
            updateSelectionInfo();
        });
    }
    
    // Handle individual contact checkboxes
    contactsTable.addEventListener('change', function(e) {
        if (e.target.classList.contains('contact-checkbox')) {
            const contactId = e.target.value;
            
            if (e.target.checked) {
                if (!selectedContactIds.includes(contactId)) {
                    selectedContactIds.push(contactId);
                }
            } else {
                const index = selectedContactIds.indexOf(contactId);
                if (index > -1) {
                    selectedContactIds.splice(index, 1);
                }
            }
            
            updateSelectAllCheckbox();
            updateSelectionInfo();
        }
    });
    
    // Handle "Select All Contacts" button
    const selectAllBtn = document.getElementById('selectAllContacts');
    if (selectAllBtn) {
        selectAllBtn.addEventListener('click', function() {
            selectedContactIds = allAvailableContacts.map(contact => contact.id.toString());
            loadContacts(); // Reload to update checkboxes
            showNotification(`Selected all ${selectedContactIds.length} contacts`, 'info');
        });
    }
    
    // Handle "Select First N" buttons
    document.querySelectorAll('.select-first-n-contacts').forEach(button => {
        button.addEventListener('click', function() {
            const count = parseInt(this.dataset.count);
            if (!count || count <= 0) return;
            
            selectedContactIds = allAvailableContacts
                .slice(0, Math.min(count, allAvailableContacts.length))
                .map(contact => contact.id.toString());
            
            loadContacts();
            showNotification(`Selected first ${selectedContactIds.length} contacts`, 'info');
        });
    });
    
    // Handle "Deselect All" button
    const deselectAllBtn = document.getElementById('deselectAllContacts');
    if (deselectAllBtn) {
        deselectAllBtn.addEventListener('click', function() {
            selectedContactIds = [];
            loadContacts();
            showNotification('Cleared all selections', 'info');
        });
    }
    
    // Handle items per page change
    const limitSelect = document.getElementById('contactLimitSelect');
    if (limitSelect) {
        limitSelect.addEventListener('change', function() {
            itemsPerPage = parseInt(this.value);
            currentPage = 1;
            loadContacts();
        });
    }
    
    // Handle group filter change
    const groupSelect = document.getElementById('contactGroupSelect');
    if (groupSelect) {
        groupSelect.addEventListener('change', function() {
            currentGroupId = this.value;
            currentPage = 1;
            loadContacts();
        });
    }
});

/**
 * Bulk Delete Functionality for Contacts
 */
window.initializeBulkDelete = function() {
    let selectedContacts = [];

    // Get DOM elements
    const selectAllCheckbox = document.getElementById('selectAllContacts');
    const contactCheckboxes = document.querySelectorAll('.contact-checkbox');
    const bulkActionsContainer = document.getElementById('bulkActionsContainer');
    const selectedCountSpan = document.getElementById('selectedCount');
    const bulkDeleteBtn = document.getElementById('bulkDeleteBtn');
    const clearSelectionBtn = document.getElementById('clearSelectionBtn');

    // Select All functionality
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            const isChecked = this.checked;
            contactCheckboxes.forEach(checkbox => {
                checkbox.checked = isChecked;
            });
            updateSelectedContacts();
        });
    }

    // Individual checkbox change
    contactCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateSelectedContacts);
    });

    // Update selected contacts array and UI
    function updateSelectedContacts() {
        selectedContacts = [];
        const checkedBoxes = document.querySelectorAll('.contact-checkbox:checked');

        checkedBoxes.forEach(checkbox => {
            selectedContacts.push({
                id: checkbox.value,
                name: checkbox.dataset.name,
                email: checkbox.dataset.email
            });
        });

        // Update UI
        if (selectedContacts.length > 0) {
            bulkActionsContainer.style.display = 'block';
            selectedCountSpan.textContent = selectedContacts.length;
        } else {
            bulkActionsContainer.style.display = 'none';
        }

        // Update select all checkbox state
        if (selectAllCheckbox) {
            if (selectedContacts.length === 0) {
                selectAllCheckbox.indeterminate = false;
                selectAllCheckbox.checked = false;
            } else if (selectedContacts.length === contactCheckboxes.length) {
                selectAllCheckbox.indeterminate = false;
                selectAllCheckbox.checked = true;
            } else {
                selectAllCheckbox.indeterminate = true;
            }
        }
    }

    // Clear selection
    if (clearSelectionBtn) {
        clearSelectionBtn.addEventListener('click', function() {
            contactCheckboxes.forEach(checkbox => {
                checkbox.checked = false;
            });
            if (selectAllCheckbox) {
                selectAllCheckbox.checked = false;
                selectAllCheckbox.indeterminate = false;
            }
            updateSelectedContacts();
        });
    }

    // Bulk delete button click
    if (bulkDeleteBtn) {
        bulkDeleteBtn.addEventListener('click', function() {
            if (selectedContacts.length === 0) {
                alert('Please select contacts to delete.');
                return;
            }

            // Populate modal with selected contacts
            const bulkDeleteCount = document.getElementById('bulkDeleteCount');
            const bulkDeletePreview = document.getElementById('bulkDeletePreview');

            if (bulkDeleteCount) {
                bulkDeleteCount.textContent = selectedContacts.length;
            }

            if (bulkDeletePreview) {
                let previewHtml = '<div class="row">';
                selectedContacts.forEach((contact, index) => {
                    if (index > 0 && index % 2 === 0) {
                        previewHtml += '</div><div class="row">';
                    }
                    previewHtml += `
                        <div class="col-md-6 mb-2">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-person-circle me-2 text-muted"></i>
                                <div>
                                    <div class="fw-bold">${window.escapeHtml(contact.name)}</div>
                                    <small class="text-muted">${window.escapeHtml(contact.email)}</small>
                                </div>
                            </div>
                        </div>
                    `;
                });
                previewHtml += '</div>';

                bulkDeletePreview.innerHTML = previewHtml;
            }

            // Show modal
            const bulkDeleteModal = document.getElementById('bulkDeleteModal');
            if (bulkDeleteModal) {
                const modal = new bootstrap.Modal(bulkDeleteModal);
                modal.show();
            }
        });
    }

    // Modal confirmation logic
    const confirmCheckbox = document.getElementById('confirmBulkDelete');
    const confirmTextInput = document.getElementById('bulkDeleteConfirmText');
    const confirmButton = document.getElementById('confirmBulkDeleteBtn');

    function updateConfirmButton() {
        const isChecked = confirmCheckbox && confirmCheckbox.checked;
        const isTextCorrect = confirmTextInput && confirmTextInput.value.toUpperCase() === 'DELETE';

        if (confirmButton) {
            confirmButton.disabled = !(isChecked && isTextCorrect);
        }
    }

    if (confirmCheckbox) {
        confirmCheckbox.addEventListener('change', updateConfirmButton);
    }

    if (confirmTextInput) {
        confirmTextInput.addEventListener('input', updateConfirmButton);
    }

    // Confirm bulk delete
    if (confirmButton) {
        confirmButton.addEventListener('click', function() {
            if (selectedContacts.length === 0) {
                alert('No contacts selected.');
                return;
            }

            // Disable button and show loading
            confirmButton.disabled = true;
            confirmButton.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Deleting...';

            // Prepare data
            const contactIds = selectedContacts.map(contact => contact.id);

            // Send AJAX request
            fetch('ajax/bulk_delete_contacts.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    contact_ids: contactIds,
                    confirmation_token: 'BULK_DELETE_CONFIRMED'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success message
                    showNotification(data.message, 'success');

                    // Show detailed results if available
                    if (data.results && data.results.failed_deletions && data.results.failed_deletions.length > 0) {
                        console.warn('Some deletions failed:', data.results.failed_deletions);
                        showNotification(data.warnings || 'Some contacts could not be deleted', 'warning');
                    }

                    // Close modal and reload page
                    const modal = bootstrap.Modal.getInstance(document.getElementById('bulkDeleteModal'));
                    if (modal) {
                        modal.hide();
                    }

                    // Reload page after short delay
                    setTimeout(() => {
                        window.location.reload();
                    }, 1500);

                } else {
                    showNotification(data.message || 'Error occurred during bulk deletion', 'error');

                    // Re-enable button
                    confirmButton.disabled = false;
                    confirmButton.innerHTML = '<i class="bi bi-trash me-2"></i>Delete Selected Contacts';
                }
            })
            .catch(error => {
                console.error('Bulk delete error:', error);
                showNotification('Network error occurred during bulk deletion', 'error');

                // Re-enable button
                confirmButton.disabled = false;
                confirmButton.innerHTML = '<i class="bi bi-trash me-2"></i>Delete Selected Contacts';
            });
        });
    }

    // Reset modal when closed
    const bulkDeleteModal = document.getElementById('bulkDeleteModal');
    if (bulkDeleteModal) {
        bulkDeleteModal.addEventListener('hidden.bs.modal', function() {
            // Reset form
            if (confirmCheckbox) confirmCheckbox.checked = false;
            if (confirmTextInput) confirmTextInput.value = '';
            if (confirmButton) {
                confirmButton.disabled = true;
                confirmButton.innerHTML = '<i class="bi bi-trash me-2"></i>Delete Selected Contacts';
            }
        });
    }

    // Notification function
    function showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(notification);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }
};